# 混合智能情报系统需求文档

## 简介

混合智能情报系统结合现有的爬虫数据（News/Reports表）和SearXNG实时搜索能力，为投资专业人士提供全面的市场情报服务。系统通过本地数据的深度分析和实时搜索的广度覆盖，确保用户不会错过任何重要的投资信息。

## 需求

### 需求1：混合数据源整合

**用户故事：** 作为投资分析师，我希望系统能够同时利用本地存储的高质量数据和实时搜索的全网数据，获得最全面的市场信息。

#### 验收标准

1. WHEN 用户创建订阅 THEN 系统应该同时从本地News/Reports表和SearXNG搜索获取相关内容
2. WHEN 本地数据匹配度高 THEN 系统应该优先使用本地数据进行分析
3. WHEN 本地数据不足 THEN 系统应该自动使用SearXNG搜索补充
4. IF 发现重要的实时信息 THEN 系统应该立即触发预警
5. WHEN 合并结果展示 THEN 应该清楚标识数据来源（本地/搜索）

### 需求2：智能订阅管理

**用户故事：** 作为投资经理，我希望基于我的投后项目自动创建智能订阅，并支持灵活的关键词配置。

#### 验收标准

1. WHEN 用户登录系统 THEN 应该基于PortfolioBLL中的投资项目自动生成订阅
2. WHEN 用户添加关键词订阅 THEN 系统应该支持本地匹配和实时搜索两种模式
3. WHEN 订阅内容过多 THEN 系统应该提供智能过滤和去重功能
4. IF 用户反馈订阅质量 THEN 系统应该学习并优化订阅配置
5. WHEN 查看订阅效果 THEN 应该显示本地匹配和搜索匹配的统计数据

### 需求3：实时搜索增强

**用户故事：** 作为投资顾问，我希望系统能够利用现有的SearXNG搜索能力，实时获取最新的市场动态。

#### 验收标准

1. WHEN 触发实时搜索 THEN 系统应该使用现有的SearXNG接口进行多引擎搜索
2. WHEN 搜索结果返回 THEN 应该进行内容增强和质量评估
3. WHEN 发现高质量内容 THEN 应该自动保存到本地数据库供后续使用
4. IF 搜索频率过高 THEN 系统应该实施智能缓存和限流
5. WHEN 搜索失败 THEN 应该有降级策略，使用本地数据继续服务

### 需求4：内容质量评估

**用户故事：** 作为数据质量管理员，我希望系统能够智能评估内容质量，确保推送给用户的都是高价值信息。

#### 验收标准

1. WHEN 处理本地数据 THEN 系统应该基于来源权威性、内容完整性等维度评分
2. WHEN 处理搜索结果 THEN 应该结合搜索引擎排名、内容相关性等因素评分
3. WHEN 内容质量低于阈值 THEN 系统应该过滤掉低质量内容
4. IF 发现重复内容 THEN 系统应该智能去重并保留最高质量版本
5. WHEN 用户查看内容 THEN 应该显示质量评分和可信度指标

### 需求5：智能预警系统

**用户故事：** 作为投资团队负责人，我希望系统能够基于混合数据源生成精准的投资预警。

#### 验收标准

1. WHEN 检测到重要事件 THEN 系统应该综合本地分析和实时搜索结果生成预警
2. WHEN 预警级别为紧急 THEN 应该在检测到后1分钟内通知用户
3. WHEN 生成预警 THEN 应该包含事件描述、影响分析、数据来源、建议措施
4. IF 同一事件有多个数据源 THEN 系统应该合并信息避免重复预警
5. WHEN 用户查看预警历史 THEN 应该提供预警准确性的统计分析

### 需求6：个性化配置

**用户故事：** 作为系统用户，我希望能够灵活配置数据源优先级和搜索策略，获得个性化的服务。

#### 验收标准

1. WHEN 用户配置订阅 THEN 应该支持设置本地数据和搜索数据的权重比例
2. WHEN 用户设置搜索偏好 THEN 应该支持选择特定的搜索引擎和数据源
3. WHEN 用户调整预警阈值 THEN 系统应该根据新配置重新评估现有内容
4. IF 用户有特殊需求 THEN 应该支持自定义搜索查询和过滤规则
5. WHEN 用户查看配置效果 THEN 应该提供数据源使用统计和效果分析

### 需求7：系统性能优化

**用户故事：** 作为系统管理员，我希望系统能够高效处理大量数据和搜索请求，保持良好的响应性能。

#### 验收标准

1. WHEN 处理本地数据查询 THEN 响应时间应该在100毫秒以内
2. WHEN 执行实时搜索 THEN 应该在5秒内返回结果
3. WHEN 系统负载较高 THEN 应该优先处理高优先级用户的请求
4. IF 搜索API不可用 THEN 系统应该自动切换到纯本地数据模式
5. WHEN 缓存命中 THEN 应该显著提高响应速度并减少外部API调用

### 需求8：投资主题发现

**用户故事：** 作为投资专业人士，我希望系统能够从混合数据源中自动发现新兴投资主题，并推荐相关的投资机会。

#### 验收标准

1. WHEN 系统分析混合数据源 THEN 应该识别出热度突然上升的投资概念和主题
2. WHEN 发现新兴主题 THEN 应该分析其投资潜力、相关公司、政策支持等维度
3. WHEN 主题热度达到阈值 THEN 系统应该主动推荐给相关用户订阅
4. IF 用户订阅投资主题 THEN 系统应该持续跟踪主题发展阶段和投资机会
5. WHEN 主题发展阶段变化 THEN 应该及时通知用户并调整推荐策略

### 需求9：主题生命周期跟踪

**用户故事：** 作为投资决策者，我希望了解投资主题的发展阶段，在合适的时机进入或退出。

#### 验收标准

1. WHEN 跟踪投资主题 THEN 系统应该识别主题处于萌芽期、成长期、成熟期或衰退期
2. WHEN 主题进入新阶段 THEN 应该分析阶段特征、投资机会和风险变化
3. WHEN 主题热度异常变化 THEN 系统应该分析原因并评估对投资的影响
4. IF 主题出现分化 THEN 应该识别细分方向和差异化投资机会
5. WHEN 生成主题报告 THEN 应该包含发展历程、当前状态、未来预测和投资建议