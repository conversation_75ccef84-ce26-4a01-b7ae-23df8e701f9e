# 深度研究Agent设计文档

## 概述

深度研究Agent是一个基于MCP服务架构的智能研究系统，通过多轮检索、发散思维、信息汇总等核心算法，为用户提供深度的投资研究能力。系统采用"问题分解→发散检索→信息汇总→深度分析→结论生成"的研究流程，确保研究的全面性和深度。

## 系统架构

### 整体架构图

```mermaid
graph TB
    subgraph "聊天界面层"
        A1[用户输入]
        A2[研究模式切换]
        A3[对话管理器]
        A4[结果展示]
    end
    
    subgraph "深度研究引擎"
        B1[问题理解模块]
        B2[发散思维引擎]
        B3[检索策略规划器]
        B4[信息汇总器]
        B5[深度分析器]
    end
    
    subgraph "MCP服务层"
        C1[News MCP服务]
        C2[Reports MCP服务]
        C3[IMS MCP服务]
        C4[SearXNG MCP服务]
    end
    
    subgraph "数据处理层"
        D1[向量化服务]
        D2[相似度计算]
        D3[内容去重]
        D4[质量评估]
    end
    
    subgraph "知识管理层"
        E1[研究上下文存储]
        E2[检索历史管理]
        E3[知识图谱构建]
        E4[结论缓存]
    end
    
    A1 --> B1
    A2 --> A3
    A3 --> B1
    
    B1 --> B2
    B2 --> B3
    B3 --> C1
    B3 --> C2
    B3 --> C3
    B3 --> C4
    
    C1 --> D1
    C2 --> D1
    C3 --> D1
    C4 --> D1
    
    D1 --> B4
    D2 --> B4
    D3 --> B4
    D4 --> B4
    
    B4 --> B5
    B5 --> E1
    B5 --> A4
    
    E1 --> E2
    E2 --> E3
    E3 --> E4
```

## 核心组件设计

### 1. 问题理解模块 (QuestionUnderstandingModule)

负责解析用户的研究问题，识别研究意图和关键要素。

```csharp
public class QuestionUnderstandingModule
{
    private readonly LLMService _llmService;
    
    public async Task<ResearchIntent> AnalyzeQuestionAsync(string userQuestion)
    {
        var prompt = $@"
分析以下投资研究问题，提取关键信息：

用户问题：{userQuestion}

请分析并返回JSON格式：
{{
    ""mainTopic"": ""主要研究主题"",
    ""subTopics"": [""子主题1"", ""子主题2""],
    ""researchType"": ""行业分析/公司研究/市场趋势/风险评估"",
    ""timeFrame"": ""时间范围"",
    ""keyEntities"": [""关键实体1"", ""关键实体2""],
    ""researchDepth"": ""浅层/中等/深度"",
    ""expectedOutputs"": [""期望输出1"", ""期望输出2""]
}}";

        var response = await _llmService.GenerateAsync(prompt);
        return JsonConvert.DeserializeObject<ResearchIntent>(response);
    }
    
    public List<string> GenerateSearchKeywords(ResearchIntent intent)
    {
        var keywords = new List<string>();
        
        // 基础关键词
        keywords.Add(intent.MainTopic);
        keywords.AddRange(intent.SubTopics);
        keywords.AddRange(intent.KeyEntities);
        
        // 根据研究类型添加专业术语
        switch (intent.ResearchType)
        {
            case "行业分析":
                keywords.AddRange(new[] { "行业报告", "市场规模", "竞争格局", "发展趋势" });
                break;
            case "公司研究":
                keywords.AddRange(new[] { "财务报告", "业务模式", "管理层", "竞争优势" });
                break;
            case "市场趋势":
                keywords.AddRange(new[] { "市场动态", "价格走势", "供需关系", "政策影响" });
                break;
            case "风险评估":
                keywords.AddRange(new[] { "风险因素", "不确定性", "监管风险", "市场风险" });
                break;
        }
        
        return keywords.Distinct().ToList();
    }
}
```

### 2. 发散思维引擎 (DivergentThinkingEngine)

实现发散思维，从多个角度扩展研究范围。

```csharp
public class DivergentThinkingEngine
{
    private readonly LLMService _llmService;
    
    public async Task<List<ResearchDimension>> GenerateResearchDimensionsAsync(ResearchIntent intent)
    {
        var dimensions = new List<ResearchDimension>();
        
        // 1. 时间维度发散
        dimensions.AddRange(await GenerateTemporalDimensionsAsync(intent));
        
        // 2. 空间维度发散
        dimensions.AddRange(await GenerateSpatialDimensionsAsync(intent));
        
        // 3. 利益相关者维度发散
        dimensions.AddRange(await GenerateStakeholderDimensionsAsync(intent));
        
        // 4. 因果关系维度发散
        dimensions.AddRange(await GenerateCausalDimensionsAsync(intent));
        
        // 5. 竞争维度发散
        dimensions.AddRange(await GenerateCompetitiveDimensionsAsync(intent));
        
        return dimensions;
    }
    
    private async Task<List<ResearchDimension>> GenerateTemporalDimensionsAsync(ResearchIntent intent)
    {
        var prompt = $@"
基于研究主题：{intent.MainTopic}

从时间维度思考，生成相关的研究角度：
1. 历史发展脉络
2. 当前状态分析
3. 未来趋势预测
4. 周期性规律
5. 关键时间节点

返回JSON数组格式的研究维度。";

        var response = await _llmService.GenerateAsync(prompt);
        return JsonConvert.DeserializeObject<List<ResearchDimension>>(response);
    }
    
    private async Task<List<ResearchDimension>> GenerateStakeholderDimensionsAsync(ResearchIntent intent)
    {
        var prompt = $@"
基于研究主题：{intent.MainTopic}

识别所有相关的利益相关者，并为每个利益相关者生成研究角度：
1. 投资者视角
2. 管理层视角
3. 客户视角
4. 供应商视角
5. 监管机构视角
6. 竞争对手视角
7. 行业专家视角

为每个视角生成具体的研究问题。";

        var response = await _llmService.GenerateAsync(prompt);
        return JsonConvert.DeserializeObject<List<ResearchDimension>>(response);
    }
    
    public async Task<List<string>> GenerateRelatedQuestionsAsync(string originalQuestion)
    {
        var prompt = $@"
原始问题：{originalQuestion}

请生成10个相关的深度研究问题，这些问题应该：
1. 从不同角度探讨同一主题
2. 挖掘潜在的影响因素
3. 探索因果关系
4. 考虑风险和机会
5. 涵盖定量和定性分析

返回JSON数组格式。";

        var response = await _llmService.GenerateAsync(prompt);
        return JsonConvert.DeserializeObject<List<string>>(response);
    }
}
```

### 3. 检索策略规划器 (RetrievalStrategyPlanner)

规划多轮检索策略，确保信息收集的全面性。

```csharp
public class RetrievalStrategyPlanner
{
    private readonly Dictionary<string, IMCPService> _mcpServices;
    
    public async Task<RetrievalPlan> CreateRetrievalPlanAsync(
        ResearchIntent intent, 
        List<ResearchDimension> dimensions)
    {
        var plan = new RetrievalPlan
        {
            ResearchIntent = intent,
            RetrievalRounds = new List<RetrievalRound>()
        };
        
        // 第一轮：基础信息收集
        plan.RetrievalRounds.Add(new RetrievalRound
        {
            RoundNumber = 1,
            Purpose = "基础信息收集",
            Services = new[] { "News", "Reports" },
            Keywords = GenerateBaseKeywords(intent),
            TimeRange = GetTimeRange(intent.TimeFrame),
            MaxResults = 50
        });
        
        // 第二轮：深度信息挖掘
        plan.RetrievalRounds.Add(new RetrievalRound
        {
            RoundNumber = 2,
            Purpose = "深度信息挖掘",
            Services = new[] { "Reports", "IMS" },
            Keywords = GenerateDeepKeywords(intent, dimensions),
            TimeRange = GetExtendedTimeRange(intent.TimeFrame),
            MaxResults = 30
        });
        
        // 第三轮：实时信息补充
        plan.RetrievalRounds.Add(new RetrievalRound
        {
            RoundNumber = 3,
            Purpose = "实时信息补充",
            Services = new[] { "SearXNG", "News" },
            Keywords = GenerateCurrentKeywords(intent),
            TimeRange = TimeRange.Recent,
            MaxResults = 20
        });
        
        // 第四轮：关联信息发现
        plan.RetrievalRounds.Add(new RetrievalRound
        {
            RoundNumber = 4,
            Purpose = "关联信息发现",
            Services = new[] { "News", "Reports", "IMS" },
            Keywords = await GenerateRelatedKeywordsAsync(intent, dimensions),
            TimeRange = GetTimeRange(intent.TimeFrame),
            MaxResults = 40
        });
        
        return plan;
    }
    
    public async Task<List<RetrievalResult>> ExecuteRetrievalPlanAsync(
        RetrievalPlan plan, 
        IProgress<RetrievalProgress> progress)
    {
        var allResults = new List<RetrievalResult>();
        
        foreach (var round in plan.RetrievalRounds)
        {
            progress?.Report(new RetrievalProgress
            {
                CurrentRound = round.RoundNumber,
                TotalRounds = plan.RetrievalRounds.Count,
                Purpose = round.Purpose,
                Status = "开始检索"
            });
            
            var roundResults = await ExecuteRetrievalRoundAsync(round, progress);
            allResults.AddRange(roundResults);
            
            // 根据当前轮次结果调整后续检索策略
            await AdjustSubsequentRoundsAsync(plan, round.RoundNumber, roundResults);
            
            progress?.Report(new RetrievalProgress
            {
                CurrentRound = round.RoundNumber,
                TotalRounds = plan.RetrievalRounds.Count,
                Purpose = round.Purpose,
                Status = $"完成，获得{roundResults.Count}条结果"
            });
        }
        
        return allResults;
    }
    
    private async Task<List<RetrievalResult>> ExecuteRetrievalRoundAsync(
        RetrievalRound round, 
        IProgress<RetrievalProgress> progress)
    {
        var results = new List<RetrievalResult>();
        var tasks = new List<Task<List<RetrievalResult>>>();
        
        // 并行调用多个MCP服务
        foreach (var serviceName in round.Services)
        {
            if (_mcpServices.ContainsKey(serviceName))
            {
                var service = _mcpServices[serviceName];
                tasks.Add(CallMCPServiceAsync(service, round, progress));
            }
        }
        
        var serviceResults = await Task.WhenAll(tasks);
        
        foreach (var serviceResult in serviceResults)
        {
            results.AddRange(serviceResult);
        }
        
        return results;
    }
    
    private async Task<List<RetrievalResult>> CallMCPServiceAsync(
        IMCPService service, 
        RetrievalRound round, 
        IProgress<RetrievalProgress> progress)
    {
        var results = new List<RetrievalResult>();
        
        foreach (var keyword in round.Keywords)
        {
            progress?.Report(new RetrievalProgress
            {
                CurrentRound = round.RoundNumber,
                Status = $"正在从{service.Name}搜索：{keyword}"
            });
            
            try
            {
                var serviceResults = await service.SearchAsync(new SearchRequest
                {
                    Query = keyword,
                    TimeRange = round.TimeRange,
                    MaxResults = round.MaxResults / round.Keywords.Count
                });
                
                results.AddRange(serviceResults.Select(r => new RetrievalResult
                {
                    Source = service.Name,
                    Keyword = keyword,
                    Content = r,
                    RetrievalTime = DateTime.Now,
                    RoundNumber = round.RoundNumber
                }));
            }
            catch (Exception ex)
            {
                progress?.Report(new RetrievalProgress
                {
                    CurrentRound = round.RoundNumber,
                    Status = $"从{service.Name}搜索{keyword}时出错：{ex.Message}"
                });
            }
        }
        
        return results;
    }
}
```

### 4. 信息汇总器 (InformationAggregator)

对多轮检索的结果进行智能汇总和去重。

```csharp
public class InformationAggregator
{
    private readonly VectorService _vectorService;
    private readonly LLMService _llmService;
    
    public async Task<AggregatedInformation> AggregateInformationAsync(
        List<RetrievalResult> retrievalResults, 
        ResearchIntent intent)
    {
        // 1. 内容去重
        var deduplicatedResults = await DeduplicateContentAsync(retrievalResults);
        
        // 2. 质量评估和筛选
        var qualityResults = await AssessAndFilterQualityAsync(deduplicatedResults);
        
        // 3. 主题聚类
        var clusteredResults = await ClusterByTopicAsync(qualityResults);
        
        // 4. 时间线构建
        var timeline = await BuildTimelineAsync(qualityResults);
        
        // 5. 关键信息提取
        var keyInsights = await ExtractKeyInsightsAsync(clusteredResults, intent);
        
        // 6. 矛盾信息识别
        var contradictions = await IdentifyContradictionsAsync(qualityResults);
        
        // 7. 信息缺口分析
        var gaps = await AnalyzeInformationGapsAsync(qualityResults, intent);
        
        return new AggregatedInformation
        {
            DeduplicatedResults = deduplicatedResults,
            QualityResults = qualityResults,
            TopicClusters = clusteredResults,
            Timeline = timeline,
            KeyInsights = keyInsights,
            Contradictions = contradictions,
            InformationGaps = gaps,
            AggregationTime = DateTime.Now
        };
    }
    
    private async Task<List<RetrievalResult>> DeduplicateContentAsync(List<RetrievalResult> results)
    {
        var deduplicatedResults = new List<RetrievalResult>();
        var processedHashes = new HashSet<string>();
        
        foreach (var result in results)
        {
            // 计算内容向量
            var vector = await _vectorService.GetVectorAsync(result.Content.Title + " " + result.Content.Summary);
            
            // 检查是否与已有内容相似
            var isDuplicate = false;
            foreach (var existingResult in deduplicatedResults)
            {
                var existingVector = await _vectorService.GetVectorAsync(
                    existingResult.Content.Title + " " + existingResult.Content.Summary);
                
                var similarity = _vectorService.CalculateSimilarity(vector, existingVector);
                if (similarity > 0.85) // 相似度阈值
                {
                    isDuplicate = true;
                    // 保留质量更高的结果
                    if (result.Content.QualityScore > existingResult.Content.QualityScore)
                    {
                        deduplicatedResults.Remove(existingResult);
                        deduplicatedResults.Add(result);
                    }
                    break;
                }
            }
            
            if (!isDuplicate)
            {
                deduplicatedResults.Add(result);
            }
        }
        
        return deduplicatedResults;
    }
    
    private async Task<Dictionary<string, List<RetrievalResult>>> ClusterByTopicAsync(
        List<RetrievalResult> results)
    {
        var clusters = new Dictionary<string, List<RetrievalResult>>();
        
        // 使用LLM进行主题聚类
        var clusteringPrompt = $@"
对以下{results.Count}条信息进行主题聚类，识别主要话题：

{string.Join("\n", results.Select((r, i) => $"{i + 1}. {r.Content.Title}"))}

返回JSON格式的聚类结果：
{{
    ""clusters"": [
        {{
            ""topic"": ""主题名称"",
            ""items"": [1, 3, 5],
            ""description"": ""主题描述""
        }}
    ]
}}";

        var clusteringResponse = await _llmService.GenerateAsync(clusteringPrompt);
        var clusteringResult = JsonConvert.DeserializeObject<ClusteringResult>(clusteringResponse);
        
        foreach (var cluster in clusteringResult.Clusters)
        {
            var clusterResults = cluster.Items.Select(i => results[i - 1]).ToList();
            clusters[cluster.Topic] = clusterResults;
        }
        
        return clusters;
    }
    
    private async Task<List<KeyInsight>> ExtractKeyInsightsAsync(
        Dictionary<string, List<RetrievalResult>> clusters, 
        ResearchIntent intent)
    {
        var insights = new List<KeyInsight>();
        
        foreach (var cluster in clusters)
        {
            var clusterContent = string.Join("\n", 
                cluster.Value.Select(r => r.Content.Title + ": " + r.Content.Summary));
            
            var insightPrompt = $@"
基于以下关于'{cluster.Key}'的信息，提取关键洞察：

{clusterContent}

请提取：
1. 核心观点
2. 重要数据
3. 趋势判断
4. 风险提示
5. 投资机会

返回JSON格式的洞察列表。";

            var insightResponse = await _llmService.GenerateAsync(insightPrompt);
            var clusterInsights = JsonConvert.DeserializeObject<List<KeyInsight>>(insightResponse);
            
            foreach (var insight in clusterInsights)
            {
                insight.Topic = cluster.Key;
                insight.SupportingEvidence = cluster.Value.Select(r => r.Content.Title).ToList();
            }
            
            insights.AddRange(clusterInsights);
        }
        
        return insights;
    }
    
    private async Task<List<Contradiction>> IdentifyContradictionsAsync(List<RetrievalResult> results)
    {
        var contradictions = new List<Contradiction>();
        
        // 使用LLM识别矛盾信息
        var contradictionPrompt = $@"
分析以下信息，识别其中的矛盾或冲突观点：

{string.Join("\n", results.Select((r, i) => 
    $"{i + 1}. [{r.Source}] {r.Content.Title}: {r.Content.Summary}"))}

识别：
1. 数据冲突
2. 观点分歧
3. 时间不一致
4. 来源可信度差异

返回JSON格式的矛盾列表。";

        var contradictionResponse = await _llmService.GenerateAsync(contradictionPrompt);
        contradictions = JsonConvert.DeserializeObject<List<Contradiction>>(contradictionResponse);
        
        return contradictions;
    }
}
```

### 5. 深度分析器 (DeepAnalyzer)

对汇总的信息进行深度分析，生成洞察和结论。

```csharp
public class DeepAnalyzer
{
    private readonly LLMService _llmService;
    private readonly VectorService _vectorService;
    
    public async Task<DeepAnalysisResult> PerformDeepAnalysisAsync(
        AggregatedInformation aggregatedInfo, 
        ResearchIntent intent)
    {
        var analysisResult = new DeepAnalysisResult();
        
        // 1. 趋势分析
        analysisResult.TrendAnalysis = await AnalyzeTrendsAsync(aggregatedInfo, intent);
        
        // 2. 因果关系分析
        analysisResult.CausalAnalysis = await AnalyzeCausalRelationshipsAsync(aggregatedInfo);
        
        // 3. 风险评估
        analysisResult.RiskAssessment = await AssessRisksAsync(aggregatedInfo, intent);
        
        // 4. 机会识别
        analysisResult.OpportunityIdentification = await IdentifyOpportunitiesAsync(aggregatedInfo, intent);
        
        // 5. 情景分析
        analysisResult.ScenarioAnalysis = await PerformScenarioAnalysisAsync(aggregatedInfo, intent);
        
        // 6. 投资建议生成
        analysisResult.InvestmentRecommendations = await GenerateInvestmentRecommendationsAsync(
            aggregatedInfo, intent, analysisResult);
        
        // 7. 置信度评估
        analysisResult.ConfidenceAssessment = await AssessConfidenceAsync(aggregatedInfo, analysisResult);
        
        return analysisResult;
    }
    
    private async Task<TrendAnalysis> AnalyzeTrendsAsync(
        AggregatedInformation aggregatedInfo, 
        ResearchIntent intent)
    {
        var trendPrompt = $@"
基于以下时间线信息，分析{intent.MainTopic}的发展趋势：

时间线：
{string.Join("\n", aggregatedInfo.Timeline.Select(t => $"{t.Date}: {t.Event}"))}

关键洞察：
{string.Join("\n", aggregatedInfo.KeyInsights.Select(i => $"- {i.Content}"))}

请分析：
1. 短期趋势（3-6个月）
2. 中期趋势（6-18个月）
3. 长期趋势（1-3年）
4. 趋势驱动因素
5. 趋势变化的关键指标

返回详细的趋势分析报告。";

        var trendResponse = await _llmService.GenerateAsync(trendPrompt);
        return JsonConvert.DeserializeObject<TrendAnalysis>(trendResponse);
    }
    
    private async Task<List<InvestmentRecommendation>> GenerateInvestmentRecommendationsAsync(
        AggregatedInformation aggregatedInfo, 
        ResearchIntent intent, 
        DeepAnalysisResult partialResult)
    {
        var recommendationPrompt = $@"
基于以下深度分析结果，为{intent.MainTopic}生成投资建议：

趋势分析：{JsonConvert.SerializeObject(partialResult.TrendAnalysis)}
风险评估：{JsonConvert.SerializeObject(partialResult.RiskAssessment)}
机会识别：{JsonConvert.SerializeObject(partialResult.OpportunityIdentification)}

请生成：
1. 投资策略建议
2. 具体投资标的
3. 投资时机建议
4. 风险控制措施
5. 预期收益评估
6. 退出策略

每个建议都要包含：
- 建议内容
- 支持理由
- 风险提示
- 执行建议
- 监控指标";

        var recommendationResponse = await _llmService.GenerateAsync(recommendationPrompt);
        return JsonConvert.DeserializeObject<List<InvestmentRecommendation>>(recommendationResponse);
    }
}
```

## 数据模型

### 核心数据结构

```csharp
public class ResearchIntent
{
    public string MainTopic { get; set; }
    public List<string> SubTopics { get; set; }
    public string ResearchType { get; set; }
    public string TimeFrame { get; set; }
    public List<string> KeyEntities { get; set; }
    public string ResearchDepth { get; set; }
    public List<string> ExpectedOutputs { get; set; }
}

public class ResearchDimension
{
    public string Name { get; set; }
    public string Description { get; set; }
    public List<string> ResearchQuestions { get; set; }
    public List<string> Keywords { get; set; }
    public string Priority { get; set; }
}

public class RetrievalPlan
{
    public ResearchIntent ResearchIntent { get; set; }
    public List<RetrievalRound> RetrievalRounds { get; set; }
    public DateTime CreatedTime { get; set; }
}

public class RetrievalRound
{
    public int RoundNumber { get; set; }
    public string Purpose { get; set; }
    public string[] Services { get; set; }
    public List<string> Keywords { get; set; }
    public TimeRange TimeRange { get; set; }
    public int MaxResults { get; set; }
}

public class AggregatedInformation
{
    public List<RetrievalResult> DeduplicatedResults { get; set; }
    public List<RetrievalResult> QualityResults { get; set; }
    public Dictionary<string, List<RetrievalResult>> TopicClusters { get; set; }
    public List<TimelineEvent> Timeline { get; set; }
    public List<KeyInsight> KeyInsights { get; set; }
    public List<Contradiction> Contradictions { get; set; }
    public List<InformationGap> InformationGaps { get; set; }
    public DateTime AggregationTime { get; set; }
}

public class DeepAnalysisResult
{
    public TrendAnalysis TrendAnalysis { get; set; }
    public CausalAnalysis CausalAnalysis { get; set; }
    public RiskAssessment RiskAssessment { get; set; }
    public OpportunityIdentification OpportunityIdentification { get; set; }
    public ScenarioAnalysis ScenarioAnalysis { get; set; }
    public List<InvestmentRecommendation> InvestmentRecommendations { get; set; }
    public ConfidenceAssessment ConfidenceAssessment { get; set; }
}
```

## 对话流程设计

### 深度研究对话流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant C as 聊天界面
    participant R as 深度研究引擎
    participant M as MCP服务
    
    U->>C: 输入研究问题
    C->>R: 启动深度研究模式
    R->>C: 显示"正在理解问题..."
    
    R->>R: 问题理解与分解
    R->>C: 显示研究计划
    C->>U: "我将从以下角度研究..."
    
    loop 多轮检索
        R->>C: 显示当前检索轮次
        R->>M: 并行调用MCP服务
        M->>R: 返回检索结果
        R->>C: 显示实时发现
        C->>U: "发现了关于X的重要信息..."
    end
    
    R->>R: 信息汇总与去重
    R->>C: 显示汇总进度
    C->>U: "正在整合来自N个数据源的信息..."
    
    R->>R: 深度分析
    R->>C: 显示分析进度
    C->>U: "正在分析趋势和风险..."
    
    R->>C: 返回分析结果
    C->>U: 展示深度分析报告
    
    U->>C: 提出后续问题
    C->>R: 基于已有研究上下文回答
    R->>C: 返回深入回答
    C->>U: 展示回答
```

这个设计文档详细描述了深度研究Agent的核心算法和实现方式，包括发散思维、多轮检索、信息汇总等关键功能。你觉得这个设计是否符合你的期望？