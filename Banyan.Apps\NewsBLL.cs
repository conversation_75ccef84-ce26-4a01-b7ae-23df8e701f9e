using Banyan.Code;
using Banyan.Domain;
using DAL.Base;
using igos_data;
using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Configuration;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Banyan.Apps
{
    public class NewsBLL : BaseDAL<News>
    {
        /// <summary>
        /// Executes a SQL query and returns a data reader
        /// </summary>
        /// <param name="sql">SQL query</param>
        /// <returns>Data reader</returns>
        public System.Data.IDataReader ExecuteReader(string sql)
        {
            // 使用Data_basic来执行ExecuteReader
            var dataBasic = new Data_basic();
            if (dataBasic.ExecuteReader(sql))
            {
                return dataBasic.DataReader;
            }
            return null;
        }
        private MemberBLL memberBll = new MemberBLL();
        private readonly AjaxResult ajaxResult = null;
        private SysLogBLL logBLL = new SysLogBLL();
        private ProjectBLL projectBll = new ProjectBLL();
        private NewsVectorizationService _vectorizationService;
        private readonly object _vectorizationServiceLock = new object();

        public NewsBLL()
        {
            ajaxResult = ResultHelper.ajaxResult;
            // 移除构造函数中的直接初始化，改为延迟初始化
        }

        /// <summary>
        /// 获取向量化服务实例（延迟初始化）
        /// </summary>
        private NewsVectorizationService VectorizationService
        {
            get
            {
                if (_vectorizationService == null)
                {
                    lock (_vectorizationServiceLock)
                    {
                        if (_vectorizationService == null)
                        {
                            _vectorizationService = new NewsVectorizationService();
                        }
                    }
                }
                return _vectorizationService;
            }
        }
        private string getStrWhere(NameValueCollection paramValues, Member user, out string sort, bool searchNameOnly = false, bool newsTab = true)
        {
            string strWhere = $" id <> -1 ";

            string startDate = WebHelper.GetValue("startdate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(startDate))
            {
                strWhere += $"AND PubTime>='{startDate}' ";
            }

            string endDate = WebHelper.GetValue("enddate", string.Empty, paramValues);
            if (!string.IsNullOrEmpty(endDate))
            {
                strWhere += $"AND PubTime<'{Convert.ToDateTime(endDate).AddDays(1).ToString("yyyy-MM-dd")}' ";
            }

            sort = " PubTime DESC, priority DESC ";

            if (!newsTab)
            {
                strWhere += " AND classify <> '政策新闻'";
            }

            string title = WebHelper.GetValue("keywords", string.Empty, paramValues);
            title = title.Replace("'", "");
            title = title.ToLower();

            if (!string.IsNullOrWhiteSpace(title))
            {
                //strWhere += $"AND (Title like '%{title}%' OR Content like '%{title}%') ";
                strWhere += $"AND (Title like '%{title}%' OR contains(content, '\"{title}\"')  ";

                if (title.Contains("."))
                {
                    var spaceName = title.Replace(".", " ");
                    var connectName = title.Replace(".", "");
                    strWhere += " or contains(content, '\"" + spaceName + "\"')";
                    strWhere += " or contains(content, '\"" + connectName + "\"')";
                }
                strWhere += ") ";
            }

            string source = WebHelper.GetValue("source", string.Empty, paramValues);
            if (!string.IsNullOrWhiteSpace(source))
            {
                strWhere += $" AND source='{source}' ";
            }

            string type = WebHelper.GetValue("type", string.Empty, paramValues);
            if (!string.IsNullOrWhiteSpace(type))
            {
                switch (type)
                {
                    case "资讯":
                        strWhere += $" AND (classify='资讯' or classify='热门')";
                        break;
                    case "国内融资":
                        strWhere += $" AND classify='融资' AND source <> '海外'";
                        break;
                    case "国际融资":
                        strWhere += $" AND classify='融资' AND source = '海外'";
                        break;
                    case "融资":
                        strWhere += $" AND classify='融资' ";
                        break;
                    case "新股":
                        strWhere += $" AND classify='新股'"; break;
                    case "政策新闻":
                        strWhere += $" AND classify='政策新闻'"; break;
                    default: break;
                }
            }
            if (string.IsNullOrWhiteSpace(title) && newsTab)// 在资讯综合页且未搜索
            {
                string tag = WebHelper.GetValue("tag", string.Empty, paramValues);

                if (!string.IsNullOrEmpty(tag) && (type == "资讯" || type == "融资" || string.IsNullOrWhiteSpace(type)))
                {//仅默认、资讯、融资三个栏目进行过滤，用户有分组则显示分组内容
                    if (type != "融资")
                    {
                        strWhere += $" AND classify <> '融资' ";
                    }
                    if (type != "政策新闻")
                    {
                        strWhere += $" AND classify <> '政策新闻' ";
                    }

                    if (tag != "默认")
                    {
                        strWhere += $" AND tag LIKE '%{tag}%' ";
                    }
                    else
                    {
                        //strWhere += $" AND classify = '资讯' ";
                        if (user.Groups != "")
                        {

                            var list = new List<string>();
                            if (user.Groups.Contains("1"))
                            {
                                list.Add("消费");
                            }
                            if (user.Groups.Contains("2") || user.Groups.Contains("7"))
                            {
                                list.Add("技术");
                            }
                            if (user.Groups.Contains("5"))
                            {
                                list.Add("医疗");
                            }

                            var tmp = "";
                            foreach (var tagItem in list)
                            {
                                if (!string.IsNullOrEmpty(tagItem))
                                {
                                    if (tmp.Length > 0)
                                    {
                                        tmp += " OR "; // 在已有条件后添加OR
                                    }
                                    tmp += $"tag LIKE '%{tagItem.Trim()}%'"; // 为每个标签添加LIKE条件
                                }
                            }
                            if (tmp != "")
                            {
                                strWhere += $" AND ({tmp} or tag is NULL or tag = '') ";
                            }
                        }
                    }
                }

            }
            return strWhere;
        }
        public News GetModel(Member user, int id, bool recommend = false)
        {
            var res = base.GetModel(id);
            var from = recommend ?  " 来自推荐 " : "";
            
            updateLog("MiniApp, Get News" + from, "view",  res.Classify + " " + res.Source, user, res.Title);
            return res;
        }
        public AjaxResult GetNews(NameValueCollection paramValues, bool newsTab = true)
        {
            #region 用户信息
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            var user = new MemberBLL().GetModelByCache(uid); //GetModelByCache(uid);
            if (user == null || user.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = user == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = user == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }
            else if (user.RealName == "作测试")
            {
                ajaxResult.data = null;
                ajaxResult.code = (int)ResultCode.success;
                return ajaxResult;
            }
            #endregion

            #region 交互基本参数
            int pageIndex = WebHelper.GetValueInt("page", 1, paramValues);

            int pageSize = WebHelper.GetValueInt("limit", 10, paramValues);
            string keyWords = WebHelper.GetValue("keywords", string.Empty, paramValues);
            string searchName = WebHelper.GetValue("searchName", string.Empty, paramValues);

            var ProjectList = new List<News>();
            string sort;
            string strWhere = getStrWhere(paramValues, user, out sort, !searchName.IsEmpty(), newsTab);

            var res = new List<News>();
            ProjectList = GetList(strWhere, pageSize, pageIndex, "id, title, content,subject, pubTime, createTime,source,tag, classify, url", sort);
            //if (newsTab)
            //{
            if (!string.IsNullOrWhiteSpace(keyWords))
            {
                var spaceName = "";
                var connectName = "";
                if (keyWords.Contains("."))
                {
                    spaceName = keyWords.Replace(".", " ");
                    connectName = keyWords.Replace(".", "");
                }
                foreach (var i in ProjectList)
                {
                    MatchSentence(keyWords, res, spaceName, connectName, i, false);
                }
            }
            else
            {
                res = ProjectList.Select(val =>
                {
                    val.Content = val.Content.Trim();
                    val.Content = val.Content.Substring(0, Math.Min(val.Content.Length, 120));

                    // 如果Subject为空，从Content生成摘要
                    if (string.IsNullOrEmpty(val.Subject) && !string.IsNullOrEmpty(val.Content))
                    {
                        var content = val.Content;
                        // 清理HTML标签
                        content = System.Text.RegularExpressions.Regex.Replace(content, @"<[^>]+>", "");
                        content = System.Text.RegularExpressions.Regex.Replace(content, @"\s+", " ");
                        content = content.Trim();

                        if (content.Length > 120)
                        {
                            var truncated = content.Substring(0, 120);
                            var lastPeriod = truncated.LastIndexOf('。');
                            if (lastPeriod > 40)
                            {
                                val.Subject = truncated.Substring(0, lastPeriod + 1);
                            }
                            else
                            {
                                val.Subject = truncated + "...";
                            }
                        }
                        else
                        {
                            val.Subject = content;
                        }
                    }

                    return val;
                }).ToList();
            }
            //}
            //else
            //{
            //    res = ProjectList;
            //}
            ajaxResult.count = GetCount(strWhere);
            #endregion

            ajaxResult.data = res;

            updateLog("MiniApp, Get News List res count " + ajaxResult.count, "view", strWhere, user);
            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;
        }
        public AjaxResult ProjectedRelatedNewsHelper(Member user, int projectId)
        {
            if (projectId <= 0)
            {
                ajaxResult.code = (int)ResultCode.paramerror;
                ajaxResult.msg = "参数不合法！";
                return ajaxResult;
            }
            var ProjectModel = projectBll.GetModelByCache(projectId);
            ajaxResult.data = GetRelatedProjects(ProjectModel.Name);
            if ((ajaxResult.data as List<News>)?.Count > 0)
            {
                updateLog("MiniApp, match project " + ProjectModel.Name + " Get News List res count " + (ajaxResult.data as List<News>)?.Count, "view", "", user);
            }
            ajaxResult.code = (int)ResultCode.success;
            return ajaxResult;
        }

        public AjaxResult ProjectedRelatedNews(NameValueCollection paramValues)
        {
            int uid = WebHelper.GetValueInt("uid", 0, paramValues);
            var user = new MemberBLL().GetLogOnUser(uid);
            if (user == null || user.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = user == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = user == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }
            int projectId = WebHelper.GetValueInt("pid", 0, paramValues);
            return ProjectedRelatedNewsHelper(user, projectId);
        }

        public AjaxResult ProjectedRelatedNewsWeb(NameValueCollection paramValues)
        {
            Member user = memberBll.GetLogOnUser();
            if (user == null || user.Status != (int)MemberStatus.enable)
            {
                ajaxResult.code = user == null ? (int)ResultCode.nomatchidentity : (int)ResultCode.accountdisable;
                ajaxResult.msg = user == null ? "无匹配的身份信息！" : "账号信息异常！";
                return ajaxResult;
            }
            int projectId = WebHelper.GetValueInt("pid", 0, paramValues);
            return ProjectedRelatedNewsHelper(user, projectId);
        }

        public List<News> GetRelatedProjects(string name)
        {
            var res = new List<News>();
            name = name.Replace("(revisit)", "").Trim();
            name = name.Replace("'", "");

            var spaceName = "";
            var connectName = "";
            var searchStr = "contains(content, '\"" + name + "\"')";
            if (name.Contains("."))
            {
                spaceName = name.Replace(".", " ");
                connectName = name.Replace(".", "");
                searchStr += " or contains(content, '\"" + spaceName + "\"')";
                searchStr += " or contains(content, '\"" + connectName + "\"')";
            }

            var tmpres = GetListBySql("select * from news as N  where N.classify<>'政策新闻' and " + searchStr + " ORDER BY N.id DESC");
            //GetListBySql("select * from news as N inner join containstable(news, content,'\""
            //+ name + "\"') as CT on N.id = CT.[KEY] where N.classify<>'政策新闻' ORDER BY N.id DESC"
            //);
            if (tmpres.Count > 6)
            {
                tmpres = GetListBySql("select * from news as N  where N.classify<>'政策新闻' and classify='融资' and " + searchStr + " ORDER BY N.id DESC");
            }
            if (tmpres.Count <= 6)
            {
                foreach (var document in tmpres)
                {
                    MatchSentence(name, res, spaceName, connectName, document);
                }
            }
            return res;
        }

        public static void MatchSentence(string name, List<News> res, string spaceName, string connectName, News document, bool addDate = true)
        {
            string content = document.Content;
            // 提取包含关键词的句子
            string[] sentences = Regex.Split(content, @"[。,，；\n!?]");
            bool match = false;
            foreach (string sentence in sentences)
            {
                if (sentence.ToLower().Contains(name.ToLower()))
                {
                    var tmp = new News();
                    tmp.Title = document.Title;
                    tmp.Content = Regex.Replace(sentence, "^<h1>", "").Replace(name, $"<span class='highlight-news'>{name}</span>") + "...";
                    tmp.Content = Regex.Replace(tmp.Content, @"^<br><br>", "", RegexOptions.Multiline);
                    if (addDate)
                    {
                        tmp.Content = $"<span class='news-date'>{document.PubTime.ToString("yyyy/MM/dd")}</span> " + tmp.Content;
                    }
                    else
                    {
                        tmp.Content = "\'" + tmp.Content + "\'";
                        tmp.Tag = document.Tag;
                        tmp.PubTime = document.PubTime;
                    }
                    //tmp.Url = document.PubTime.ToString("yyyy/MM/dd");
                    tmp.Id = document.Id;
                    tmp.Url = document.Url;
                    tmp.Classify = document.Classify;
                    tmp.Source = document.Source;
                    res.Add(tmp);
                    match = true;
                    break;
                }
                if (name.Contains("."))
                {
                    if (sentence.ToLower().Contains(spaceName.ToLower()) || sentence.ToLower().Contains(connectName.ToLower()))
                    {
                        var tmp = new News();
                        tmp.Title = document.Title;
                        tmp.Content = sentence.Replace(name, $"<span class='highlight-news'>{name}</span>") + "...";
                        if (addDate)
                        {
                            tmp.Content = $"<span class='news-date'>{document.PubTime.ToString("yyyy/MM/dd")}</span> " + tmp.Content;
                        }
                        else
                        {
                            tmp.Content = "\"" + tmp.Content + "\"" + document.Subject;
                            tmp.Tag = document.Tag;
                            tmp.Classify = document.Classify;
                            tmp.PubTime = document.PubTime;
                        }
                        //tmp.Url = document.PubTime.ToString("yyyy/MM/dd");
                        tmp.Id = document.Id;
                        res.Add(tmp);
                        match = true;
                        break;
                    }
                }
            }
            if(!match)
            {
                res.Add(document);
            }
        }

        public void updateLog(string page, string action, string description, Member user, string project = "")
        {
            SysLog log = new SysLog
            {
                Page = page,
                Action = action,
                Name = project.Substring(0, Math.Min(project.Length, 200)),
                Project = project,
                Description = description,
                CreatedBy = user == null ? "" : user.RealName,
                Ip = Utility.WebHelper.GetIP(),
                CreatorId = user == null ? 0 : user.Id,
            };
            logBLL.Add(log);
        }

        /// <summary>
        /// 获取新闻向量化统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public AjaxResult GetVectorizationStats()
        {
            try
            {
                var stats = new
                {
                    TotalNews = GetCount("id <> -1"),
                    VectorizedNews = GetCount("VectorStatus = 1"),
                    PendingNews = GetCount("VectorStatus = 0"),
                    FailedNews = GetCount("VectorStatus = 2"),
                    VectorizationRate = GetCount("VectorStatus = 1") * 100.0 / Math.Max(GetCount("id <> -1"), 1)
                };

                ajaxResult.data = stats;
                ajaxResult.code = (int)ResultCode.success;
                return ajaxResult;
            }
            catch (Exception ex)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = $"获取向量化统计失败: {ex.Message}";
                return ajaxResult;
            }
        }

        /// <summary>
        /// 手动触发新闻向量化
        /// </summary>
        /// <param name="batchSize">批量大小</param>
        /// <returns>处理结果</returns>
        public async Task<AjaxResult> TriggerVectorizationAsync(int batchSize = 100)
        {
            try
            {
                ajaxResult.msg = "开始向量化处理...";
                ajaxResult.code = (int)ResultCode.success;

                // 异步执行向量化任务
                _ = Task.Run(async () =>
                {
                    try
                    {
                        var result = await VectorizationService.ScanAndVectorizeNewsAsync(batchSize, this);

                        // 记录处理结果
                        var logMessage = $"新闻向量化完成，总计: {result.TotalProcessed}，成功: {result.SuccessCount}，失败: {result.FailedCount}，耗时: {result.Duration.TotalSeconds:F2}秒";
                        updateLog("News Vectorization", "batch_process", logMessage, null);
                    }
                    catch (Exception ex)
                    {
                        var errorMessage = $"新闻向量化失败: {ex.Message}";
                        updateLog("News Vectorization", "error", errorMessage, null);
                    }
                });

                return ajaxResult;
            }
            catch (Exception ex)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = $"触发向量化失败: {ex.Message}";
                return ajaxResult;
            }
        }

        /// <summary>
        /// 获取新闻向量
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <returns>新闻向量</returns>
        public async Task<double[]> GetNewsVectorAsync(int newsId)
        {
            return await VectorizationService.GetNewsVectorAsync(newsId, this);
        }

        /// <summary>
        /// 批量获取新闻向量
        /// </summary>
        /// <param name="newsIds">新闻ID列表</param>
        /// <returns>新闻向量字典</returns>
        public async Task<Dictionary<int, double[]>> GetNewsVectorsBatchAsync(List<int> newsIds)
        {
            return await VectorizationService.GetNewsVectorsBatchAsync(newsIds, this);
        }

        /// <summary>
        /// 重新向量化指定新闻
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> ReVectorizeNewsAsync(int newsId)
        {
            try
            {
                var news = GetModel(newsId);
                if (news == null)
                {
                    return false;
                }

                return await VectorizationService.VectorizeSingleNewsAsync(news, this);
            }
            catch (Exception ex)
            {
                updateLog("News ReVectorization", "error", $"重新向量化新闻失败，新闻ID: {newsId}，错误: {ex.Message}", null);
                return false;
            }
        }

        /// <summary>
        /// 根据向量搜索相似新闻
        /// </summary>
        /// <param name="queryVector">查询向量</param>
        /// <param name="limit">返回结果数量限制</param>
        /// <param name="threshold">相似度阈值（0-1之间）</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>相似新闻列表</returns>
        public async Task<AjaxResult> SearchSimilarNewsAsync(
            double[] queryVector,
            int limit = 10,
            double threshold = 0.5,
            NewsSearchFilters filters = null)
        {
            try
            {
                var searchService = new NewsVectorSearch();
                var results = await searchService.SearchSimilarNews(queryVector, limit, threshold, filters);

                ajaxResult.data = results;
                ajaxResult.count = results.Count();
                ajaxResult.code = (int)ResultCode.success;
                return ajaxResult;
            }
            catch (Exception ex)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = $"搜索相似新闻失败: {ex.Message}";
                return ajaxResult;
            }
        }

        /// <summary>
        /// 根据文本搜索相似新闻
        /// </summary>
        /// <param name="queryText">查询文本</param>
        /// <param name="limit">返回结果数量限制</param>
        /// <param name="threshold">相似度阈值（0-1之间）</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>相似新闻列表</returns>
        public async Task<AjaxResult> SearchSimilarNewsByTextAsync(
            string queryText,
            int limit = 10,
            double threshold = 0.5,
            NewsSearchFilters filters = null)
        {
            try
            {
                var searchService = new NewsVectorSearch();
                var results = await searchService.SearchSimilarNewsByText(queryText, limit, threshold, filters);

                ajaxResult.data = results;
                ajaxResult.count = results.Count();
                ajaxResult.code = (int)ResultCode.success;
                return ajaxResult;
            }
            catch (Exception ex)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = $"根据文本搜索相似新闻失败: {ex.Message}";
                return ajaxResult;
            }
        }

        /// <summary>
        /// 获取与特定新闻相似的新闻
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        /// <param name="limit">返回结果数量限制</param>
        /// <param name="threshold">相似度阈值（0-1之间）</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>相似新闻列表</returns>
        public async Task<AjaxResult> GetSimilarNewsAsync(
            int newsId,
            int limit = 10,
            double threshold = 0.5,
            NewsSearchFilters filters = null)
        {
            try
            {
                var searchService = new NewsVectorSearch();
                var results = await searchService.GetSimilarNews(newsId, limit, threshold, filters);

                ajaxResult.data = results;
                ajaxResult.count = results.Count();
                ajaxResult.code = (int)ResultCode.success;
                return ajaxResult;
            }
            catch (Exception ex)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = $"获取相似新闻失败: {ex.Message}";
                return ajaxResult;
            }
        }

        /// <summary>
        /// 获取向量化失败的新闻列表
        /// </summary>
        /// <param name="pageSize">页大小</param>
        /// <param name="pageIndex">页码</param>
        /// <returns>新闻列表</returns>
        public AjaxResult GetFailedVectorizationNews(int pageSize = 10, int pageIndex = 1)
        {
            try
            {
                var where = "VectorStatus = 2";
                var orderBy = "VectorUpdateTime DESC";
                var fields = "Id, Title, VectorError, VectorUpdateTime, CreateTime";

                var newsList = GetList(where, pageSize, pageIndex, fields, orderBy);
                var totalCount = GetCount(where);

                var result = new
                {
                    List = newsList,
                    TotalCount = totalCount,
                    PageSize = pageSize,
                    PageIndex = pageIndex
                };

                ajaxResult.data = result;
                ajaxResult.count = totalCount;
                ajaxResult.code = (int)ResultCode.success;
                return ajaxResult;
            }
            catch (Exception ex)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = $"获取失败新闻列表失败: {ex.Message}";
                return ajaxResult;
            }
        }

        /// <summary>
        /// 获取待向量化的新闻列表
        /// </summary>
        /// <param name="pageSize">页大小</param>
        /// <param name="pageIndex">页码</param>
        /// <returns>新闻列表</returns>
        public AjaxResult GetPendingVectorizationNews(int pageSize = 10, int pageIndex = 1)
        {
            try
            {
                var where = "VectorStatus = 0";
                var orderBy = "CreateTime DESC";
                var fields = "Id, Title, CreateTime, PubTime, Source, Classify";

                var newsList = GetList(where, pageSize, pageIndex, fields, orderBy);
                var totalCount = GetCount(where);

                var result = new
                {
                    List = newsList,
                    TotalCount = totalCount,
                    PageSize = pageSize,
                    PageIndex = pageIndex
                };

                ajaxResult.data = result;
                ajaxResult.count = totalCount;
                ajaxResult.code = (int)ResultCode.success;
                return ajaxResult;
            }
            catch (Exception ex)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = $"获取待向量化新闻列表失败: {ex.Message}";
                return ajaxResult;
            }
        }

        /// <summary>
        /// 获取指定字段的不同值列表
        /// </summary>
        /// <param name="fieldName">字段名</param>
        /// <returns>不同值列表</returns>
        public List<string> GetDistinctValues(string fieldName)
        {
            try
            {
                if (string.IsNullOrEmpty(fieldName))
                {
                    return new List<string>();
                }

                // 构建SQL查询
                var sql = $"SELECT DISTINCT {fieldName} FROM News WHERE {fieldName} IS NOT NULL AND {fieldName} <> '' ORDER BY {fieldName}";

                // 执行查询
                var result = new List<string>();
                using (var reader = ExecuteReader(sql))
                {
                    while (reader.Read())
                    {
                        if (!reader.IsDBNull(0))
                        {
                            result.Add(reader.GetString(0));
                        }
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取字段 {fieldName} 的不同值列表失败: {ex.Message}", ex);
                return new List<string>();
            }
        }

        /// <summary>
        /// 获取新闻分类列表
        /// </summary>
        /// <returns>分类列表</returns>
        public List<string> GetCategories()
        {
            try
            {
                // 获取所有新闻分类
                var categories = GetDistinctValues("Classify");

                // 如果没有分类数据，返回默认分类
                if (categories == null || categories.Count == 0)
                {
                    categories = new List<string> { "资讯", "融资", "新股", "政策新闻", "热门" };
                }

                return categories;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取新闻分类列表失败: {ex.Message}", ex);
                return new List<string> { "资讯", "融资", "新股", "政策新闻", "热门" };
            }
        }

        /// <summary>
        /// 获取新闻来源列表
        /// </summary>
        /// <returns>来源列表</returns>
        public List<string> GetSources()
        {
            try
            {
                // 从数据库获取所有新闻来源
                return GetDistinctValues("Source");
            }
            catch (Exception ex)
            {
                Logger.Error($"获取新闻来源列表失败: {ex.Message}", ex);
                return new List<string>();
            }
        }

        /// <summary>
        /// 根据用户兴趣获取推荐新闻
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="limit">返回结果数量限制</param>
        /// <param name="threshold">相似度阈值（0-1之间）</param>
        /// <param name="filters">过滤条件</param>
        /// <returns>推荐新闻列表</returns>
        public async Task<AjaxResult> GetRecommendedNewsByInterestAsync(
            int userId,
            int limit = 10,
            double threshold = 0.4,
            NewsSearchFilters filters = null)
        {
            try
            {
                var searchService = new NewsVectorSearch();
                var results = await searchService.GetRecommendedNewsByInterest(userId, limit, threshold, filters);

                ajaxResult.data = results;
                ajaxResult.count = results.Count();
                ajaxResult.code = (int)ResultCode.success;
                return ajaxResult;
            }
            catch (Exception ex)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = $"获取用户推荐新闻失败: {ex.Message}";
                return ajaxResult;
            }
        }

        /// <summary>
        /// 记录用户阅读新闻
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="newsId">新闻ID</param>
        /// <returns>操作结果</returns>
        public async Task<AjaxResult> RecordUserReadNewsAsync(int userId, int newsId)
        {
            try
            {
                var searchService = new NewsVectorSearch();
                var success = await searchService.RecordUserReadNewsAsync(userId, newsId);

                ajaxResult.data = success;
                ajaxResult.code = (int)ResultCode.success;
                return ajaxResult;
            }
            catch (Exception ex)
            {
                ajaxResult.code = (int)ResultCode.exception;
                ajaxResult.msg = $"记录用户阅读新闻失败: {ex.Message}";
                return ajaxResult;
            }
        }



        #region 新闻向量化自动触发

        /// <summary>
        /// 重写Add方法，添加新闻后自动触发向量化
        /// </summary>
        /// <param name="model">新闻对象</param>
        /// <returns>新闻ID</returns>
        public override object Add(News model)
        {
            try
            {
                // 调用基类的Add方法添加新闻
                int newsId = Convert.ToInt32(base.Add(model));

                if (newsId > 0)
                {
                    // 异步触发向量化处理
                    TriggerNewsVectorizationAsync(newsId);
                }

                return newsId;
            }
            catch (Exception e)
            {
                Logger.Error($"新闻{model.Title}添加失败", e);
                throw (e);
            }
        }

        /// <summary>
        /// 重写Update方法，更新新闻后自动触发向量化
        /// </summary>
        /// <param name="model">新闻对象</param>
        /// <returns>是否成功</returns>
        public override bool Update(News model)
        {
            // 获取更新前的新闻内容
            var oldNews = GetModel(model.Id);

            // 调用基类的Update方法更新新闻
            bool success = base.Update(model);

            if (success)
            {
                // 检查内容是否发生变化，如果变化则触发向量化
                if (oldNews != null &&
                    (oldNews.Title != model.Title ||
                     oldNews.Content != model.Content ||
                     oldNews.Subject != model.Subject ||
                     oldNews.Tag != model.Tag ||
                     oldNews.Classify != model.Classify))
                {
                    // 异步触发向量化处理
                    TriggerNewsVectorizationAsync(model.Id);
                }
            }

            return success;
        }

        /// <summary>
        /// 异步触发新闻向量化处理
        /// </summary>
        /// <param name="newsId">新闻ID</param>
        private void TriggerNewsVectorizationAsync(int newsId)
        {
            try
            {
                // 使用Task.Run异步执行，避免阻塞当前线程
                Task.Run(async () =>
                {
                    try
                    {
                        // 获取新闻对象
                        var news = GetModel(newsId);
                        if (news == null)
                        {
                            Logger.Error($"触发新闻向量化失败：新闻不存在，ID: {newsId}");
                            return;
                        }

                        Logger.Info($"自动触发新闻向量化，新闻ID: {newsId}，标题: {news.Title}");

                        // 执行向量化处理
                        bool success = await VectorizationService.VectorizeSingleNewsAsync(news, this);

                        if (success)
                        {
                            Logger.Info($"新闻自动向量化成功，新闻ID: {newsId}");
                        }
                        else
                        {
                            Logger.Error($"新闻自动向量化失败，新闻ID: {newsId}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"新闻自动向量化异常，新闻ID: {newsId}，错误: {ex.Message}", ex);
                    }
                });
            }
            catch (Exception ex)
            {
                Logger.Error($"触发新闻向量化失败，新闻ID: {newsId}，错误: {ex.Message}", ex);
            }
        }

        #endregion
    }
}