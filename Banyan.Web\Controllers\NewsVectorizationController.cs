using System;
using System.Collections.Specialized;
using System.Threading.Tasks;
using System.Web.Mvc;
using System.Linq;
using Banyan.Apps;
using Banyan.Code;
using Banyan.Domain;
using Banyan.Web.Filters;

namespace Banyan.Web.Controllers
{
    /// <summary>
    /// 新闻向量化控制器
    /// 提供新闻向量化相关的API接口
    /// </summary>
    [AuthFilter]
    public class NewsVectorizationController : BaseController
    {
        private readonly NewsBLL _newsBLL;

        public NewsVectorizationController()
        {
            _newsBLL = new NewsBLL();
        }

        /// <summary>
        /// 获取向量化统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        [HttpPost]
        public ActionResult GetVectorizationStats()
        {
            try
            {
                var result = _newsBLL.GetVectorizationStats();
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error("获取向量化统计失败", ex);
                return Json(new { code = 1, msg = "获取统计信息失败" }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 手动触发新闻向量化
        /// </summary>
        /// <returns>处理结果</returns>
        [HttpPost]
        public async Task<ActionResult> TriggerVectorization()
        {
            try
            {
                var batchSize = WebHelper.GetValueInt("batchSize", 100, Request.Form);
                var result = await _newsBLL.TriggerVectorizationAsync(batchSize);
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error("触发向量化失败", ex);
                return Json(new { code = 1, msg = "触发向量化失败" }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 重新向量化指定新闻
        /// </summary>
        /// <returns>处理结果</returns>
        [HttpPost]
        public async Task<ActionResult> ReVectorizeNews()
        {
            try
            {
                var newsId = WebHelper.GetValueInt("newsId", 0, Request.Form);
                if (newsId <= 0)
                {
                    return Json(new { code = 2, msg = "新闻ID参数错误" }, JsonRequestBehavior.AllowGet);
                }

                var success = await _newsBLL.ReVectorizeNewsAsync(newsId);
                if (success)
                {
                    return Json(new { code = 0, msg = "重新向量化成功" }, JsonRequestBehavior.AllowGet);
                }
                else
                {
                    return Json(new { code = 1, msg = "重新向量化失败" }, JsonRequestBehavior.AllowGet);
                }
            }
            catch (Exception ex)
            {
                Logger.Error("重新向量化新闻失败", ex);
                return Json(new { code = 1, msg = "重新向量化失败" }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 获取向量化失败的新闻列表
        /// </summary>
        /// <returns>新闻列表</returns>
        [HttpPost]
        public ActionResult GetFailedVectorizationNews()
        {
            try
            {
                var pageSize = WebHelper.GetValueInt("pageSize", 10, Request.Form);
                var pageIndex = WebHelper.GetValueInt("pageIndex", 1, Request.Form);
                
                var result = _newsBLL.GetFailedVectorizationNews(pageSize, pageIndex);
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error("获取失败新闻列表失败", ex);
                return Json(new { code = 1, msg = "获取失败新闻列表失败" }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 获取待向量化的新闻列表
        /// </summary>
        /// <returns>新闻列表</returns>
        [HttpPost]
        public ActionResult GetPendingVectorizationNews()
        {
            try
            {
                var pageSize = WebHelper.GetValueInt("pageSize", 10, Request.Form);
                var pageIndex = WebHelper.GetValueInt("pageIndex", 1, Request.Form);
                
                var result = _newsBLL.GetPendingVectorizationNews(pageSize, pageIndex);
                return Json(result, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error("获取待向量化新闻列表失败", ex);
                return Json(new { code = 1, msg = "获取待向量化新闻列表失败" }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 获取新闻向量
        /// </summary>
        /// <returns>新闻向量</returns>
        [HttpPost]
        public async Task<ActionResult> GetNewsVector()
        {
            try
            {
                var newsId = WebHelper.GetValueInt("newsId", 0, Request.Form);
                if (newsId <= 0)
                {
                    return Json(new { code = 2, msg = "新闻ID参数错误" }, JsonRequestBehavior.AllowGet);
                }

                var vector = await _newsBLL.GetNewsVectorAsync(newsId);
                if (vector != null)
                {
                    return Json(new { code = 0, data = vector, msg = "获取新闻向量成功" }, JsonRequestBehavior.AllowGet);
                }
                else
                {
                    return Json(new { code = 1, msg = "新闻向量不存在" }, JsonRequestBehavior.AllowGet);
                }
            }
            catch (Exception ex)
            {
                Logger.Error("获取新闻向量失败", ex);
                return Json(new { code = 1, msg = "获取新闻向量失败" }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 批量获取新闻向量
        /// </summary>
        /// <returns>新闻向量字典</returns>
        [HttpPost]
        public async Task<ActionResult> GetNewsVectorsBatch()
        {
            try
            {
                var newsIdsStr = WebHelper.GetValue("newsIds", "", Request.Form);
                if (string.IsNullOrEmpty(newsIdsStr))
                {
                    return Json(new { code = 2, msg = "新闻ID列表参数错误" }, JsonRequestBehavior.AllowGet);
                }

                var newsIds = newsIdsStr.Split(',').Select(id => int.Parse(id.Trim())).ToList();
                var vectors = await _newsBLL.GetNewsVectorsBatchAsync(newsIds);
                
                return Json(new { code = 0, data = vectors, msg = "获取新闻向量成功" }, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                Logger.Error("批量获取新闻向量失败", ex);
                return Json(new { code = 1, msg = "批量获取新闻向量失败" }, JsonRequestBehavior.AllowGet);
            }
        }

        /// <summary>
        /// 向量化管理页面
        /// </summary>
        /// <returns>管理页面</returns>
        [HttpGet]
        public ActionResult Index()
        {
            var user = new MemberBLL().GetLogOnUser();
            if (user == null || user.Levels != (byte)MemberLevels.Administrator)
            {
                return RedirectToAction("Index", "Login");
            }
            
            ViewData["manager"] = user;
            return View();
        }
    }
} 