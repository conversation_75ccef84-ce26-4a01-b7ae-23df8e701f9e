# 混合智能情报系统设计文档

## 概述

混合智能情报系统充分利用现有的技术资产：本地爬虫数据（News/Reports表）、SearXNG搜索服务、向量化系统和用户画像，构建一个全面的投资情报平台。系统通过智能的数据源选择和内容融合，为用户提供既有深度又有广度的市场情报。

## 系统架构

### 架构图

```mermaid
graph TB
    subgraph "数据输入层"
        A1[本地News表]
        A2[本地Reports表]
        A3[SearXNG搜索API]
        A4[用户投资项目]
    end
    
    subgraph "数据处理层"
        B1[混合数据获取服务]
        B2[内容质量评估器]
        B3[智能去重合并器]
        B4[向量化处理器]
    end
    
    subgraph "分析引擎层"
        C1[订阅匹配引擎]
        C2[情感分析模块]
        C3[事件检测模块]
        C4[影响评估模块]
        C5[投资主题发现引擎]
        C6[主题生命周期跟踪器]
    end
    
    subgraph "智能服务层"
        D1[预警生成服务]
        D2[个性化推荐服务]
        D3[实时监控服务]
        D4[报告生成服务]
    end
    
    subgraph "用户界面层"
        E1[订阅管理界面]
        E2[预警中心]
        E3[情报仪表板]
        E4[移动端应用]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> C1
    
    B1 --> B2
    B2 --> B3
    B3 --> B4
    B4 --> C1
    
    C1 --> C2
    C1 --> C3
    C1 --> C4
    C1 --> C5
    C5 --> C6
    
    C2 --> D1
    C3 --> D1
    C4 --> D1
    C5 --> D1
    C6 --> D1
    
    D1 --> E2
    D2 --> E3
    D3 --> E1
    D4 --> E3
```

## 核心组件设计

### 1. 混合数据获取服务 (HybridDataService)

```csharp
public class HybridDataService
{
    private readonly NewsBLL _newsBLL;
    private readonly ReportsBLL _reportsBLL;
    private readonly LLMController _llmController; // 复用现有的搜索功能
    private readonly ICache _cache;
    
    public async Task<List<IntelligenceContent>> GetContentForSubscriptionAsync(
        UserSubscription subscription, 
        DateTime since)
    {
        var allContent = new List<IntelligenceContent>();
        
        // 1. 获取本地数据（优先级高，速度快）
        var localContent = await GetLocalContentAsync(subscription, since);
        allContent.AddRange(localContent);
        
        // 2. 根据配置决定是否需要搜索补充
        if (ShouldPerformSearch(subscription, localContent))
        {
            var searchContent = await GetSearchContentAsync(subscription, since);
            allContent.AddRange(searchContent);
        }
        
        // 3. 智能去重和质量评估
        var processedContent = await ProcessAndDeduplicateAsync(allContent);
        
        return processedContent.OrderByDescending(c => c.QualityScore).ToList();
    }
    
    private async Task<List<IntelligenceContent>> GetLocalContentAsync(
        UserSubscription subscription, 
        DateTime since)
    {
        var content = new List<IntelligenceContent>();
        
        // 从News表获取相关内容
        var newsWhere = BuildWhereClause(subscription, since);
        var newsList = _newsBLL.GetList(newsWhere, 100, 1, "*", "PubTime DESC");
        
        foreach (var news in newsList)
        {
            var relevanceScore = await CalculateRelevanceAsync(news.Title + " " + news.Content, subscription.Target);
            if (relevanceScore >= subscription.Config.SimilarityThreshold)
            {
                content.Add(new IntelligenceContent
                {
                    Title = news.Title,
                    Content = news.Content,
                    PublishTime = news.PubTime,
                    Source = news.Source,
                    Url = news.Url,
                    DataSource = "Local_News",
                    ContentType = news.Classify,
                    RelevanceScore = relevanceScore,
                    QualityScore = CalculateLocalContentQuality(news)
                });
            }
        }
        
        // 从Reports表获取相关内容（如果存在）
        if (_reportsBLL != null)
        {
            var reportsList = _reportsBLL.GetList(newsWhere, 50, 1, "*", "CreateTime DESC");
            foreach (var report in reportsList)
            {
                var relevanceScore = await CalculateRelevanceAsync(report.Title + " " + report.Content, subscription.Target);
                if (relevanceScore >= subscription.Config.SimilarityThreshold)
                {
                    content.Add(new IntelligenceContent
                    {
                        Title = report.Title,
                        Content = report.Content,
                        PublishTime = report.CreateTime,
                        Source = report.Source ?? "研报",
                        DataSource = "Local_Reports",
                        ContentType = "研报",
                        RelevanceScore = relevanceScore,
                        QualityScore = CalculateLocalContentQuality(report)
                    });
                }
            }
        }
        
        return content;
    }
    
    private async Task<List<IntelligenceContent>> GetSearchContentAsync(
        UserSubscription subscription, 
        DateTime since)
    {
        var content = new List<IntelligenceContent>();
        
        try
        {
            // 构建搜索查询
            var searchQuery = BuildSearchQuery(subscription);
            
            // 复用现有的SearXNG搜索功能
            var searchResults = await _llmController.PerformWebSearchAsync(searchQuery, 20);
            
            foreach (var result in searchResults)
            {
                // 检查是否已存在于本地数据库
                if (!await IsContentExistsLocallyAsync(result.Url))
                {
                    var relevanceScore = await CalculateRelevanceAsync(result.Title + " " + result.Content, subscription.Target);
                    if (relevanceScore >= subscription.Config.SimilarityThreshold)
                    {
                        content.Add(new IntelligenceContent
                        {
                            Title = result.Title,
                            Content = result.Content,
                            PublishTime = DateTime.Now, // 搜索结果通常没有精确时间
                            Source = ExtractSourceFromUrl(result.Url),
                            Url = result.Url,
                            DataSource = "Search_" + result.Engine,
                            ContentType = "搜索资讯",
                            RelevanceScore = relevanceScore,
                            QualityScore = CalculateSearchContentQuality(result)
                        });
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Logger.Error($"搜索内容获取失败: {ex.Message}", ex);
            // 搜索失败不影响本地数据的使用
        }
        
        return content;
    }
    
    private bool ShouldPerformSearch(UserSubscription subscription, List<IntelligenceContent> localContent)
    {
        // 智能决策是否需要搜索
        
        // 1. 如果本地内容足够且质量高，可能不需要搜索
        if (localContent.Count >= 10 && localContent.Average(c => c.QualityScore) > 0.8)
        {
            return false;
        }
        
        // 2. 如果是特殊关键词或新项目，需要搜索补充
        if (subscription.SubscriptionType == SubscriptionType.Keyword || 
            subscription.CreatedTime > DateTime.Now.AddDays(-7))
        {
            return true;
        }
        
        // 3. 根据用户配置决定
        return subscription.Config.EnableRealTimeSearch;
    }
    
    private string BuildSearchQuery(UserSubscription subscription)
    {
        var query = subscription.Target;
        
        // 根据订阅类型优化搜索查询
        switch (subscription.SubscriptionType)
        {
            case SubscriptionType.Project:
                query += " 投资 融资 项目";
                break;
            case SubscriptionType.Keyword:
                query += " 新闻 资讯";
                break;
            case SubscriptionType.Industry:
                query += " 行业 分析 报告";
                break;
        }
        
        // 添加时间限制
        query += " site:finance.sina.com.cn OR site:finance.eastmoney.com OR site:wallstreetcn.com";
        
        return query;
    }
}
```

### 2. 智能订阅管理服务 (IntelligentSubscriptionService)

```csharp
public class IntelligentSubscriptionService
{
    private readonly ProjectBLL _projectBLL;
    private readonly HybridDataService _hybridDataService;
    
    public async Task<List<UserSubscription>> GenerateSmartSubscriptionsAsync(int userId)
    {
        var subscriptions = new List<UserSubscription>();
        
        // 1. 基于用户投资项目生成订阅
        var userProjects = await _projectBLL.GetUserProjectsAsync(userId);
        foreach (var project in userProjects)
        {
            subscriptions.Add(new UserSubscription
            {
                UserId = userId,
                SubscriptionType = SubscriptionType.Project,
                Target = project.Name,
                ProjectId = project.Id,
                Config = new SubscriptionConfig
                {
                    SimilarityThreshold = 0.6,
                    EnableRealTimeSearch = true,
                    LocalDataWeight = 0.7,
                    SearchDataWeight = 0.3,
                    MaxResultsPerDay = 50
                }
            });
            
            // 为每个项目生成相关关键词订阅
            var relatedKeywords = await GenerateRelatedKeywordsAsync(project);
            foreach (var keyword in relatedKeywords)
            {
                subscriptions.Add(new UserSubscription
                {
                    UserId = userId,
                    SubscriptionType = SubscriptionType.Keyword,
                    Target = keyword,
                    ProjectId = project.Id,
                    Config = new SubscriptionConfig
                    {
                        SimilarityThreshold = 0.5,
                        EnableRealTimeSearch = true,
                        LocalDataWeight = 0.5,
                        SearchDataWeight = 0.5,
                        MaxResultsPerDay = 20
                    }
                });
            }
        }
        
        return subscriptions;
    }
    
    private async Task<List<string>> GenerateRelatedKeywordsAsync(Project project)
    {
        var keywords = new List<string>();
        
        // 基于项目信息生成相关关键词
        if (!string.IsNullOrEmpty(project.Industry))
        {
            keywords.Add(project.Industry);
        }
        
        if (!string.IsNullOrEmpty(project.Tags))
        {
            keywords.AddRange(project.Tags.Split(',').Select(t => t.Trim()));
        }
        
        // 可以使用AI生成更多相关关键词
        // var aiKeywords = await GenerateAIKeywordsAsync(project.Description);
        // keywords.AddRange(aiKeywords);
        
        return keywords.Distinct().ToList();
    }
}
```

### 3. 内容质量评估器 (ContentQualityAssessor)

```csharp
public class ContentQualityAssessor
{
    public double CalculateLocalContentQuality(News news)
    {
        double score = 0.5; // 基础分
        
        // 来源权威性 (30%)
        score += GetSourceAuthorityScore(news.Source) * 0.3;
        
        // 内容完整性 (25%)
        score += GetContentCompletenessScore(news) * 0.25;
        
        // 时效性 (20%)
        score += GetTimelinessScore(news.PubTime) * 0.2;
        
        // 分类相关性 (15%)
        score += GetCategoryRelevanceScore(news.Classify) * 0.15;
        
        // 向量化状态 (10%)
        score += (news.VectorStatus == 1 ? 0.1 : 0);
        
        return Math.Min(score, 1.0);
    }
    
    public double CalculateSearchContentQuality(WebSearchResult result)
    {
        double score = 0.3; // 搜索结果基础分较低
        
        // 搜索引擎排名 (40%)
        score += GetSearchRankingScore(result.Engine) * 0.4;
        
        // 内容增强状态 (30%)
        score += (result.IsContentEnhanced ? 0.3 : 0.1);
        
        // URL权威性 (20%)
        score += GetUrlAuthorityScore(result.Url) * 0.2;
        
        // 标题质量 (10%)
        score += GetTitleQualityScore(result.Title) * 0.1;
        
        return Math.Min(score, 1.0);
    }
    
    private double GetSourceAuthorityScore(string source)
    {
        var authorityMap = new Dictionary<string, double>
        {
            ["新华社"] = 1.0,
            ["人民日报"] = 1.0,
            ["中国证券报"] = 0.9,
            ["上海证券报"] = 0.9,
            ["证券时报"] = 0.9,
            ["21世纪经济报道"] = 0.8,
            ["第一财经"] = 0.8,
            ["财新网"] = 0.8,
            ["新浪财经"] = 0.7,
            ["东方财富"] = 0.7,
            ["腾讯财经"] = 0.6,
            ["网易财经"] = 0.6
        };
        
        return authorityMap.GetValueOrDefault(source, 0.4);
    }
    
    private double GetUrlAuthorityScore(string url)
    {
        if (string.IsNullOrEmpty(url)) return 0.2;
        
        var authorityDomains = new Dictionary<string, double>
        {
            ["xinhuanet.com"] = 1.0,
            ["people.com.cn"] = 1.0,
            ["cs.com.cn"] = 0.9,
            ["cnstock.com"] = 0.9,
            ["stcn.com"] = 0.9,
            ["21jingji.com"] = 0.8,
            ["yicai.com"] = 0.8,
            ["caixin.com"] = 0.8,
            ["sina.com.cn"] = 0.7,
            ["eastmoney.com"] = 0.7,
            ["qq.com"] = 0.6,
            ["163.com"] = 0.6
        };
        
        foreach (var domain in authorityDomains)
        {
            if (url.Contains(domain.Key))
            {
                return domain.Value;
            }
        }
        
        return 0.3;
    }
}
```

### 4. 智能预警生成器 (IntelligentAlertGenerator)

```csharp
public class IntelligentAlertGenerator
{
    private readonly ContentQualityAssessor _qualityAssessor;
    private readonly SentimentAnalysisService _sentimentAnalysis;
    
    public async Task<List<IntelligenceAlert>> GenerateAlertsAsync(
        List<IntelligenceContent> contents, 
        UserSubscription subscription)
    {
        var alerts = new List<IntelligenceAlert>();
        
        foreach (var content in contents)
        {
            // 1. 基础相关性检查
            if (content.RelevanceScore < subscription.Config.SimilarityThreshold)
                continue;
            
            // 2. 质量评估
            if (content.QualityScore < 0.5)
                continue;
            
            // 3. 情感分析
            var sentiment = await _sentimentAnalysis.AnalyzeAsync(content.Content);
            
            // 4. 事件重要性评估
            var importance = CalculateImportanceScore(content, sentiment);
            
            // 5. 确定预警级别
            var alertLevel = DetermineAlertLevel(importance, content.QualityScore, sentiment);
            
            if (alertLevel != AlertLevel.None)
            {
                alerts.Add(new IntelligenceAlert
                {
                    UserId = subscription.UserId,
                    SubscriptionId = subscription.Id,
                    Title = GenerateAlertTitle(content, alertLevel),
                    Description = GenerateAlertDescription(content, sentiment),
                    AlertLevel = alertLevel,
                    AlertType = DetermineAlertType(content, sentiment),
                    ImpactScore = importance,
                    DataSource = content.DataSource,
                    ContentUrl = content.Url,
                    CreatedTime = DateTime.Now
                });
            }
        }
        
        return alerts;
    }
    
    private AlertLevel DetermineAlertLevel(double importance, double quality, SentimentResult sentiment)
    {
        var score = importance * 0.5 + quality * 0.3 + Math.Abs(sentiment.Score) * 0.2;
        
        if (score >= 0.9) return AlertLevel.Emergency;
        if (score >= 0.7) return AlertLevel.Important;
        if (score >= 0.5) return AlertLevel.Attention;
        if (score >= 0.3) return AlertLevel.Information;
        
        return AlertLevel.None;
    }
    
    private string GenerateAlertTitle(IntelligenceContent content, AlertLevel level)
    {
        var prefix = level switch
        {
            AlertLevel.Emergency => "🚨 紧急",
            AlertLevel.Important => "⚠️ 重要",
            AlertLevel.Attention => "📢 关注",
            AlertLevel.Information => "ℹ️ 信息",
            _ => ""
        };
        
        var source = content.DataSource.StartsWith("Local") ? "本地数据" : "实时搜索";
        
        return $"{prefix} [{source}] {content.Title}";
    }
}
```

## 数据模型

### 统一内容模型
```csharp
public class IntelligenceContent
{
    public string Title { get; set; }
    public string Content { get; set; }
    public DateTime PublishTime { get; set; }
    public string Source { get; set; }
    public string Url { get; set; }
    public string DataSource { get; set; } // Local_News, Local_Reports, Search_Google, etc.
    public string ContentType { get; set; }
    public double RelevanceScore { get; set; }
    public double QualityScore { get; set; }
    public Dictionary<string, object> Metadata { get; set; }
}
```

### 订阅配置扩展
```csharp
public class SubscriptionConfig
{
    public double SimilarityThreshold { get; set; } = 0.6;
    public bool EnableRealTimeSearch { get; set; } = true;
    public double LocalDataWeight { get; set; } = 0.7;
    public double SearchDataWeight { get; set; } = 0.3;
    public int MaxResultsPerDay { get; set; } = 50;
    public List<string> PreferredSources { get; set; } = new List<string>();
    public List<string> ExcludedSources { get; set; } = new List<string>();
    public AlertLevel MinAlertLevel { get; set; } = AlertLevel.Information;
}
```

### 5. 投资主题发现引擎 (InvestmentThemeDiscoveryEngine)

```csharp
public class InvestmentThemeDiscoveryEngine
{
    private readonly HybridDataService _hybridDataService;
    private readonly VectorService _vectorService;
    private readonly LLMService _llmService;
    
    public async Task<List<EmergingTheme>> DiscoverEmergingThemesAsync(int daysBack = 30)
    {
        // 1. 从混合数据源获取最近的内容
        var recentContent = await _hybridDataService.GetRecentContentAsync(daysBack);
        
        // 2. 提取热词和概念
        var hotConcepts = await ExtractHotConceptsAsync(recentContent);
        
        // 3. 分析概念的投资相关性
        var investmentConcepts = await FilterInvestmentRelevantConceptsAsync(hotConcepts);
        
        // 4. 识别新兴主题
        var emergingThemes = await IdentifyEmergingThemesAsync(investmentConcepts);
        
        // 5. 评估主题投资潜力
        return await EvaluateInvestmentPotentialAsync(emergingThemes);
    }
    
    private async Task<List<HotConcept>> ExtractHotConceptsAsync(List<IntelligenceContent> content)
    {
        var conceptFrequency = new Dictionary<string, ConceptStats>();
        
        foreach (var item in content)
        {
            // 使用LLM提取关键概念
            var concepts = await _llmService.ExtractKeyConceptsAsync(item.Content);
            
            foreach (var concept in concepts)
            {
                if (!conceptFrequency.ContainsKey(concept))
                {
                    conceptFrequency[concept] = new ConceptStats();
                }
                
                conceptFrequency[concept].Frequency++;
                conceptFrequency[concept].RecentMentions.Add(new ConceptMention
                {
                    Date = item.PublishTime,
                    Source = item.DataSource,
                    Context = item.Title
                });
            }
        }
        
        // 识别热度突然上升的概念
        return conceptFrequency
            .Where(kvp => IsHotConcept(kvp.Value))
            .Select(kvp => new HotConcept
            {
                Name = kvp.Key,
                Stats = kvp.Value,
                HotScore = CalculateHotScore(kvp.Value)
            })
            .OrderByDescending(c => c.HotScore)
            .ToList();
    }
    
    private async Task<List<InvestmentConcept>> FilterInvestmentRelevantConceptsAsync(List<HotConcept> hotConcepts)
    {
        var investmentConcepts = new List<InvestmentConcept>();
        
        foreach (var concept in hotConcepts)
        {
            // 使用LLM判断投资相关性
            var relevanceAnalysis = await _llmService.AnalyzeInvestmentRelevanceAsync(concept.Name);
            
            if (relevanceAnalysis.IsInvestmentRelevant)
            {
                investmentConcepts.Add(new InvestmentConcept
                {
                    Name = concept.Name,
                    HotScore = concept.HotScore,
                    InvestmentRelevance = relevanceAnalysis.RelevanceScore,
                    InvestmentAngles = relevanceAnalysis.InvestmentAngles,
                    RelatedIndustries = relevanceAnalysis.RelatedIndustries
                });
            }
        }
        
        return investmentConcepts;
    }
    
    private async Task<List<EmergingTheme>> IdentifyEmergingThemesAsync(List<InvestmentConcept> concepts)
    {
        var themes = new List<EmergingTheme>();
        
        // 使用聚类算法将相关概念组合成主题
        var conceptVectors = new Dictionary<string, float[]>();
        foreach (var concept in concepts)
        {
            conceptVectors[concept.Name] = await _vectorService.GetTextEmbeddingAsync(concept.Name);
        }
        
        var clusters = ClusterConcepts(conceptVectors);
        
        foreach (var cluster in clusters)
        {
            var theme = await GenerateThemeFromClusterAsync(cluster, concepts);
            if (theme != null)
            {
                themes.Add(theme);
            }
        }
        
        return themes;
    }
    
    private async Task<EmergingTheme> GenerateThemeFromClusterAsync(
        List<string> conceptCluster, 
        List<InvestmentConcept> allConcepts)
    {
        var clusterConcepts = allConcepts.Where(c => conceptCluster.Contains(c.Name)).ToList();
        
        // 使用LLM生成主题名称和描述
        var themeAnalysis = await _llmService.GenerateThemeAnalysisAsync(clusterConcepts);
        
        return new EmergingTheme
        {
            Name = themeAnalysis.ThemeName,
            Description = themeAnalysis.Description,
            RelatedConcepts = clusterConcepts,
            DiscoveryDate = DateTime.Now,
            HotLevel = clusterConcepts.Average(c => c.HotScore),
            InvestmentPotential = themeAnalysis.InvestmentPotential,
            RelatedCompanies = await FindRelatedCompaniesAsync(themeAnalysis.ThemeName),
            KeyDrivers = themeAnalysis.KeyDrivers,
            RiskFactors = themeAnalysis.RiskFactors
        };
    }
}
```

### 6. 主题生命周期跟踪器 (ThemeLifecycleTracker)

```csharp
public class ThemeLifecycleTracker
{
    private readonly HybridDataService _hybridDataService;
    private readonly LLMService _llmService;
    
    public async Task<ThemeLifecycleStatus> TrackThemeLifecycleAsync(string themeName)
    {
        // 1. 收集主题相关的历史数据
        var historicalData = await _hybridDataService.GetThemeHistoricalDataAsync(themeName, 180); // 6个月
        
        // 2. 分析主题发展阶段
        var currentStage = await AnalyzeThemeStageAsync(themeName, historicalData);
        
        // 3. 预测发展趋势
        var futureTrend = await PredictThemeTrendAsync(themeName, historicalData, currentStage);
        
        // 4. 识别投资机会和风险
        var opportunities = await IdentifyInvestmentOpportunitiesAsync(themeName, currentStage);
        var risks = await IdentifyInvestmentRisksAsync(themeName, currentStage);
        
        return new ThemeLifecycleStatus
        {
            ThemeName = themeName,
            CurrentStage = currentStage,
            StageConfidence = CalculateStageConfidence(historicalData, currentStage),
            FutureTrend = futureTrend,
            InvestmentOpportunities = opportunities,
            InvestmentRisks = risks,
            RecommendedActions = GenerateRecommendedActions(currentStage, futureTrend),
            LastUpdated = DateTime.Now
        };
    }
    
    private async Task<ThemeStage> AnalyzeThemeStageAsync(string themeName, List<IntelligenceContent> historicalData)
    {
        // 分析多个指标来判断主题发展阶段
        var indicators = new ThemeStageIndicators
        {
            MediaAttention = CalculateMediaAttentionTrend(historicalData),
            SentimentEvolution = CalculateSentimentEvolution(historicalData),
            PolicySupport = await AnalyzePolicySupportAsync(themeName, historicalData),
            MarketReaction = await AnalyzeMarketReactionAsync(themeName, historicalData),
            TechnologyMaturity = await AnalyzeTechnologyMaturityAsync(themeName, historicalData)
        };
        
        // 使用规则引擎或机器学习模型判断阶段
        return DetermineThemeStage(indicators);
    }
    
    private ThemeStage DetermineThemeStage(ThemeStageIndicators indicators)
    {
        // 萌芽期：媒体关注度低，技术概念为主，政策刚开始关注
        if (indicators.MediaAttention.CurrentLevel < 0.3 && 
            indicators.TechnologyMaturity < 0.4 && 
            indicators.PolicySupport.SupportLevel < 0.5)
        {
            return ThemeStage.Emerging;
        }
        
        // 成长期：媒体关注度快速上升，政策支持增强，开始有商业化应用
        if (indicators.MediaAttention.GrowthRate > 0.5 && 
            indicators.PolicySupport.SupportLevel > 0.6 && 
            indicators.TechnologyMaturity > 0.4)
        {
            return ThemeStage.Growing;
        }
        
        // 成熟期：媒体关注度稳定，技术相对成熟，市场反应理性
        if (indicators.MediaAttention.CurrentLevel > 0.7 && 
            indicators.MediaAttention.GrowthRate < 0.2 && 
            indicators.TechnologyMaturity > 0.7)
        {
            return ThemeStage.Mature;
        }
        
        // 衰退期：媒体关注度下降，新概念出现替代
        if (indicators.MediaAttention.GrowthRate < -0.3)
        {
            return ThemeStage.Declining;
        }
        
        return ThemeStage.Growing; // 默认为成长期
    }
    
    private async Task<List<InvestmentOpportunity>> IdentifyInvestmentOpportunitiesAsync(
        string themeName, 
        ThemeStage currentStage)
    {
        var opportunities = new List<InvestmentOpportunity>();
        
        switch (currentStage)
        {
            case ThemeStage.Emerging:
                // 萌芽期：关注技术突破、政策催化
                opportunities.Add(new InvestmentOpportunity
                {
                    Type = "技术先锋",
                    Description = "关注掌握核心技术的早期公司",
                    RiskLevel = "高",
                    PotentialReturn = "极高",
                    TimeHorizon = "3-5年"
                });
                break;
                
            case ThemeStage.Growing:
                // 成长期：关注商业化落地、规模扩张
                opportunities.Add(new InvestmentOpportunity
                {
                    Type = "商业化领军者",
                    Description = "关注率先实现商业化的公司",
                    RiskLevel = "中高",
                    PotentialReturn = "高",
                    TimeHorizon = "2-3年"
                });
                break;
                
            case ThemeStage.Mature:
                // 成熟期：关注行业整合、效率提升
                opportunities.Add(new InvestmentOpportunity
                {
                    Type = "行业整合者",
                    Description = "关注具备整合能力的龙头企业",
                    RiskLevel = "中",
                    PotentialReturn = "中等",
                    TimeHorizon = "1-2年"
                });
                break;
        }
        
        return opportunities;
    }
}
```

### 数据模型扩展

```csharp
public class EmergingTheme
{
    public string Name { get; set; }
    public string Description { get; set; }
    public List<InvestmentConcept> RelatedConcepts { get; set; }
    public DateTime DiscoveryDate { get; set; }
    public double HotLevel { get; set; }
    public double InvestmentPotential { get; set; }
    public List<string> RelatedCompanies { get; set; }
    public List<string> KeyDrivers { get; set; }
    public List<string> RiskFactors { get; set; }
    public ThemeStage CurrentStage { get; set; }
}

public class ThemeLifecycleStatus
{
    public string ThemeName { get; set; }
    public ThemeStage CurrentStage { get; set; }
    public double StageConfidence { get; set; }
    public ThemeTrend FutureTrend { get; set; }
    public List<InvestmentOpportunity> InvestmentOpportunities { get; set; }
    public List<InvestmentRisk> InvestmentRisks { get; set; }
    public List<string> RecommendedActions { get; set; }
    public DateTime LastUpdated { get; set; }
}

public enum ThemeStage
{
    Emerging,   // 萌芽期
    Growing,    // 成长期
    Mature,     // 成熟期
    Declining   // 衰退期
}
```

这个增强版的混合智能情报系统现在具备了：

1. **原有功能**：混合数据源、智能订阅、质量评估、预警系统
2. **新增功能**：投资主题发现、主题生命周期跟踪、投资机会识别

这样的设计充分利用了现有的技术资产，既保持了系统的稳定性，又大大扩展了智能分析能力。你觉得这个增强方案如何？